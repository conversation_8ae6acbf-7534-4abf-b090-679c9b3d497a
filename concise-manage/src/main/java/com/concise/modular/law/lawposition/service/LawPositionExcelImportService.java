package com.concise.modular.law.lawposition.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.common.util.PoiUtil;
import com.concise.modular.evaluation.cityareainfo.entity.CityAreaInfo;
import com.concise.modular.evaluation.cityareainfo.service.CityAreaInfoService;
import com.concise.modular.law.lawposition.dto.LawPositionExcelDto;
import com.concise.modular.law.lawposition.entity.LawPosition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 普法阵地Excel导入服务
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Slf4j
@Service
public class LawPositionExcelImportService {

    @Resource
    private LawPositionService lawPositionService;

    @Resource
    private CityAreaInfoService cityAreaInfoService;

    /**
     * 阵地类型映射
     */
    private static final Map<String, String> POSITION_TYPE_MAP = new HashMap<>();
    
    /**
     * 阵地级别映射
     */
    private static final Map<String, String> POSITION_LEVEL_MAP = new HashMap<>();

    static {
        // 阵地类型映射
        POSITION_TYPE_MAP.put("法治文化基地", "1");
        POSITION_TYPE_MAP.put("法治文化公园", "1");
        POSITION_TYPE_MAP.put("普法教育基地", "2");
        POSITION_TYPE_MAP.put("普法教育中心", "2");
        POSITION_TYPE_MAP.put("法治宣传点", "3");
        POSITION_TYPE_MAP.put("民主法治村", "4");

        // 阵地级别映射
        POSITION_LEVEL_MAP.put("省级", "1");
        POSITION_LEVEL_MAP.put("市级", "2");
        POSITION_LEVEL_MAP.put("区级", "3");
        POSITION_LEVEL_MAP.put("县级", "3");
        POSITION_LEVEL_MAP.put("乡镇级", "4");
        POSITION_LEVEL_MAP.put("街道级", "4");
    }

    /**
     * 从Excel文件导入普法阵地数据
     *
     * @param file Excel文件
     * @return 导入结果
     */
    public Map<String, Object> importFromExcel(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMessages = new ArrayList<>();
        int successCount = 0;
        int errorCount = 0;

        try {
            // 解析Excel文件
            List<LawPositionExcelDto> excelDataList = PoiUtil.importExcel(file, 0, 1, LawPositionExcelDto.class);
            
            if (CollectionUtil.isEmpty(excelDataList)) {
                result.put("success", false);
                result.put("message", "Excel文件为空或格式不正确");
                return result;
            }

            // 验证和转换数据
            for (int i = 0; i < excelDataList.size(); i++) {
                LawPositionExcelDto excelDto = excelDataList.get(i);
                int rowNum = i + 2; // Excel行号（从第2行开始）

                try {
                    // 验证必填字段
                    List<String> validationErrors = validateRequiredFields(excelDto, rowNum);
                    if (!validationErrors.isEmpty()) {
                        errorMessages.addAll(validationErrors);
                        errorCount++;
                        continue;
                    }

                    // 转换为LawPosition实体
                    LawPosition lawPosition = convertToLawPosition(excelDto);
                    if (lawPosition == null) {
                        errorMessages.add("第" + rowNum + "行：数据转换失败");
                        errorCount++;
                        continue;
                    }

                    // 检查重复数据
                    if (isDuplicate(lawPosition)) {
                        errorMessages.add("第" + rowNum + "行：阵地名称已存在 - " + lawPosition.getPositionName());
                        errorCount++;
                        continue;
                    }

                    // 保存数据
                    lawPositionService.save(lawPosition);
                    successCount++;

                } catch (Exception e) {
                    log.error("处理第{}行数据时发生异常：{}", rowNum, e.getMessage(), e);
                    errorMessages.add("第" + rowNum + "行：处理异常 - " + e.getMessage());
                    errorCount++;
                }
            }

        } catch (Exception e) {
            log.error("Excel导入过程中发生异常：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "导入过程中发生异常：" + e.getMessage());
            return result;
        }

        // 构建返回结果
        result.put("success", errorCount == 0);
        result.put("successCount", successCount);
        result.put("errorCount", errorCount);
        result.put("totalCount", successCount + errorCount);
        result.put("errorMessages", errorMessages);
        
        if (errorCount == 0) {
            result.put("message", "导入成功，共导入" + successCount + "条数据");
        } else {
            result.put("message", "导入完成，成功" + successCount + "条，失败" + errorCount + "条");
        }

        return result;
    }

    /**
     * 验证必填字段
     */
    private List<String> validateRequiredFields(LawPositionExcelDto dto, int rowNum) {
        List<String> errors = new ArrayList<>();

        if (StrUtil.isBlank(dto.getPositionName())) {
            errors.add("第" + rowNum + "行：阵地名称不能为空");
        }
        if (StrUtil.isBlank(dto.getCity())) {
            errors.add("第" + rowNum + "行：所在城市不能为空");
        }
        if (StrUtil.isBlank(dto.getArea())) {
            errors.add("第" + rowNum + "行：所在地区不能为空");
        }
        if (StrUtil.isBlank(dto.getPositionLevel())) {
            errors.add("第" + rowNum + "行：阵地级别不能为空");
        }
        if (StrUtil.isBlank(dto.getPositionType())) {
            errors.add("第" + rowNum + "行：阵地类型不能为空");
        }

        return errors;
    }

    /**
     * 转换Excel数据为LawPosition实体
     */
    private LawPosition convertToLawPosition(LawPositionExcelDto dto) {
        LawPosition lawPosition = new LawPosition();

        // 基本信息
        lawPosition.setPositionName(dto.getPositionName().trim());
        lawPosition.setCity(dto.getCity().trim());
        lawPosition.setArea(dto.getArea().trim());
        lawPosition.setDescription(StrUtil.isNotBlank(dto.getDescription()) ? dto.getDescription().trim() : null);
        lawPosition.setAddressDetail(StrUtil.isNotBlank(dto.getAddressDetail()) ? dto.getAddressDetail().trim() : null);
        lawPosition.setOrgName(StrUtil.isNotBlank(dto.getOrgName()) ? dto.getOrgName().trim() : null);

        // 转换阵地类型
        String positionType = POSITION_TYPE_MAP.get(dto.getPositionType().trim());
        if (positionType == null) {
            log.warn("未知的阵地类型：{}", dto.getPositionType());
            return null;
        }
        lawPosition.setPositionType(positionType);

        // 转换阵地级别
        String positionLevel = POSITION_LEVEL_MAP.get(dto.getPositionLevel().trim());
        if (positionLevel == null) {
            log.warn("未知的阵地级别：{}", dto.getPositionLevel());
            return null;
        }
        lawPosition.setPositionLevel(positionLevel);

        // 生成地址编码和地址名称
        generateAddressInfo(lawPosition);

        return lawPosition;
    }

    /**
     * 生成地址编码和地址名称
     */
    private void generateAddressInfo(LawPosition lawPosition) {
        try {
            // 查找城市和地区的编码
            String cityCode = findAreaCode(lawPosition.getCity(), 2);
            String areaCode = findAreaCode(lawPosition.getArea(), 3);

            if (StrUtil.isNotBlank(cityCode) && StrUtil.isNotBlank(areaCode)) {
                // 生成地址编码（省,市,区）
                String provinceCode = "330000"; // 默认浙江省
                lawPosition.setAddressCode(provinceCode + "," + cityCode + "," + areaCode);
                
                // 生成地址名称
                lawPosition.setAddressName("浙江省" + lawPosition.getCity() + lawPosition.getArea());
            }
        } catch (Exception e) {
            log.warn("生成地址信息失败：{}", e.getMessage());
        }
    }

    /**
     * 查找区域编码
     */
    private String findAreaCode(String areaName, int level) {
        try {
            List<CityAreaInfo> areaInfoList = cityAreaInfoService.list(
                new QueryWrapper<CityAreaInfo>()
                    .eq("level", level)
                    .like("area", areaName)
            );
            
            if (CollectionUtil.isNotEmpty(areaInfoList)) {
                return areaInfoList.get(0).getId();
            }
        } catch (Exception e) {
            log.warn("查找区域编码失败：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 检查是否重复
     */
    private boolean isDuplicate(LawPosition lawPosition) {
        long count = lawPositionService.count(
            new QueryWrapper<LawPosition>()
                .eq("position_name", lawPosition.getPositionName())
                .eq("city", lawPosition.getCity())
                .eq("area", lawPosition.getArea())
        );
        return count > 0;
    }
}
