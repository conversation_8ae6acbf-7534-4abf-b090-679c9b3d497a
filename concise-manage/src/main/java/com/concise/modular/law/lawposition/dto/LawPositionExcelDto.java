package com.concise.modular.law.lawposition.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 普法阵地Excel导入数据传输对象
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Data
public class LawPositionExcelDto {

    /**
     * 阵地名称
     */
    @Excel(name = "阵地名称", width = 30)
    private String positionName;

    /**
     * 所在城市
     */
    @Excel(name = "所在城市", width = 20)
    private String city;

    /**
     * 所在地区
     */
    @Excel(name = "所在地区", width = 20)
    private String area;

    /**
     * 阵地级别
     */
    @Excel(name = "阵地级别", width = 15)
    private String positionLevel;

    /**
     * 阵地类型
     */
    @Excel(name = "阵地类型", width = 20)
    private String positionType;

    /**
     * 详细地址（可选）
     */
    @Excel(name = "详细地址", width = 50)
    private String addressDetail;

    /**
     * 简介（可选）
     */
    @Excel(name = "简介", width = 100)
    private String description;

    /**
     * 所属单位（可选）
     */
    @Excel(name = "所属单位", width = 30)
    private String orgName;
}
