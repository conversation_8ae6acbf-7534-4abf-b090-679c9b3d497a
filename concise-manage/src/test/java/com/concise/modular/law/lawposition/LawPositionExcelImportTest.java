package com.concise.modular.law.lawposition;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.ConciseManageApplication;
import com.concise.modular.law.lawposition.entity.LawPosition;
import com.concise.modular.law.lawposition.service.LawPositionService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 普法阵地Excel导入功能测试类
 *
 * <AUTHOR>
 * @date 2024-12-28
 */
@Slf4j
@SpringBootTest(classes = ConciseManageApplication.class)
@ActiveProfiles("test")
@Transactional // 测试后自动回滚，不影响数据库
public class LawPositionExcelImportTest {

    @Resource
    private LawPositionService lawPositionService;

    private String tempDir;
    private String testExcelPath;

    /**
     * Excel导入数据传输对象
     */
    @Data
    public static class LawPositionExcelDto {
        @Excel(name = "阵地名称", width = 30)
        private String positionName;

        @Excel(name = "所在城市", width = 20)
        private String city;

        @Excel(name = "所在地区", width = 20)
        private String area;

        @Excel(name = "阵地级别", width = 15)
        private String positionLevel;

        @Excel(name = "阵地类型", width = 20)
        private String positionType;

        @Excel(name = "详细地址", width = 50)
        private String addressDetail;

        @Excel(name = "简介", width = 100)
        private String description;
    }

    @BeforeEach
    void setUp() {
        tempDir = System.getProperty("java.io.tmpdir");
        testExcelPath = tempDir + File.separator + "test_law_position.xlsx";
        log.info("测试Excel文件路径：{}", testExcelPath);
    }

    /**
     * 测试Excel文件的基本导入功能
     */
    @Test
    void testImportExcelFile() {
        log.info("开始测试Excel文件导入功能");

        // 1. 创建测试数据
        List<LawPositionExcelDto> testData = createTestExcelData();
        
        // 2. 生成Excel文件
        createTestExcelFile(testData, testExcelPath);
        
        // 3. 导入Excel文件
        List<LawPositionExcelDto> importedData = importExcelFile(testExcelPath);
        
        // 4. 验证导入结果
        assertNotNull(importedData, "导入的数据不应为空");
        assertEquals(testData.size(), importedData.size(), "导入的数据条数应该与原始数据一致");
        
        // 验证第一条数据
        LawPositionExcelDto firstData = importedData.get(0);
        assertEquals("杭州市法治文化公园", firstData.getPositionName());
        assertEquals("杭州市", firstData.getCity());
        assertEquals("西湖区", firstData.getArea());
        assertEquals("市级", firstData.getPositionLevel());
        assertEquals("法治文化基地", firstData.getPositionType());

        log.info("Excel文件导入功能测试通过");
    }

    /**
     * 测试数据处理和保存逻辑
     */
    @Test
    void testProcessAndSaveData() {
        log.info("开始测试数据处理和保存逻辑");

        // 1. 创建测试数据
        List<LawPositionExcelDto> testData = createTestExcelData();
        
        // 2. 转换并保存数据
        List<LawPosition> savedPositions = new ArrayList<>();
        for (LawPositionExcelDto dto : testData) {
            LawPosition lawPosition = convertToLawPosition(dto);
            lawPositionService.save(lawPosition);
            savedPositions.add(lawPosition);
        }
        
        // 3. 验证保存结果
        assertEquals(testData.size(), savedPositions.size(), "保存的数据条数应该正确");
        
        // 4. 从数据库查询验证
        for (LawPosition position : savedPositions) {
            LawPosition dbPosition = lawPositionService.getById(position.getId());
            assertNotNull(dbPosition, "数据库中应该存在保存的数据");
            assertEquals(position.getPositionName(), dbPosition.getPositionName());
            assertEquals(position.getCity(), dbPosition.getCity());
            assertEquals(position.getArea(), dbPosition.getArea());
        }

        log.info("数据处理和保存逻辑测试通过");
    }

    /**
     * 测试地址字段的处理逻辑
     */
    @Test
    void testAddressProcessing() {
        log.info("开始测试地址字段处理逻辑");

        // 创建测试数据
        LawPositionExcelDto dto = new LawPositionExcelDto();
        dto.setPositionName("测试阵地");
        dto.setCity("杭州市");
        dto.setArea("西湖区");
        dto.setPositionLevel("市级");
        dto.setPositionType("法治文化基地");
        dto.setAddressDetail("文三路123号");

        // 转换数据
        LawPosition lawPosition = convertToLawPosition(dto);

        // 验证地址处理
        assertNotNull(lawPosition.getAddressName(), "地址名称应该被生成");
        assertTrue(lawPosition.getAddressName().contains("浙江省"), "地址名称应该包含省份");
        assertTrue(lawPosition.getAddressName().contains("杭州市"), "地址名称应该包含城市");
        assertTrue(lawPosition.getAddressName().contains("西湖区"), "地址名称应该包含地区");
        
        assertEquals("文三路123号", lawPosition.getAddressDetail(), "详细地址应该正确设置");

        log.info("地址字段处理逻辑测试通过");
    }

    /**
     * 测试异常数据的处理机制
     */
    @Test
    void testInvalidDataHandling() {
        log.info("开始测试异常数据处理机制");

        // 测试空数据
        LawPositionExcelDto emptyDto = new LawPositionExcelDto();
        List<String> errors = validateRequiredFields(emptyDto);
        assertTrue(errors.size() >= 5, "空数据应该产生多个验证错误");

        // 测试部分缺失数据
        LawPositionExcelDto partialDto = new LawPositionExcelDto();
        partialDto.setPositionName("测试阵地");
        partialDto.setCity("杭州市");
        // 缺少area, positionLevel, positionType
        
        errors = validateRequiredFields(partialDto);
        assertTrue(errors.size() >= 3, "部分缺失数据应该产生相应的验证错误");

        // 测试无效的阵地类型
        LawPositionExcelDto invalidTypeDto = createValidDto();
        invalidTypeDto.setPositionType("无效类型");
        
        LawPosition result = convertToLawPosition(invalidTypeDto);
        assertNull(result, "无效的阵地类型应该返回null");

        log.info("异常数据处理机制测试通过");
    }

    /**
     * 测试重复数据检查
     */
    @Test
    void testDuplicateDataCheck() {
        log.info("开始测试重复数据检查");

        // 创建并保存第一条数据
        LawPosition firstPosition = new LawPosition();
        firstPosition.setPositionName("重复测试阵地");
        firstPosition.setCity("杭州市");
        firstPosition.setArea("西湖区");
        firstPosition.setPositionType("1");
        firstPosition.setPositionLevel("2");
        lawPositionService.save(firstPosition);

        // 检查重复
        boolean isDuplicate = checkDuplicate("重复测试阵地", "杭州市", "西湖区");
        assertTrue(isDuplicate, "应该检测到重复数据");

        // 检查不重复的数据
        boolean isNotDuplicate = checkDuplicate("不重复阵地", "宁波市", "海曙区");
        assertFalse(isNotDuplicate, "不重复的数据应该返回false");

        log.info("重复数据检查测试通过");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试Excel数据
     */
    private List<LawPositionExcelDto> createTestExcelData() {
        List<LawPositionExcelDto> dataList = new ArrayList<>();

        LawPositionExcelDto dto1 = new LawPositionExcelDto();
        dto1.setPositionName("杭州市法治文化公园");
        dto1.setCity("杭州市");
        dto1.setArea("西湖区");
        dto1.setPositionLevel("市级");
        dto1.setPositionType("法治文化基地");
        dto1.setAddressDetail("文三路123号");
        dto1.setDescription("杭州市重要的法治文化宣传基地");
        dataList.add(dto1);

        LawPositionExcelDto dto2 = new LawPositionExcelDto();
        dto2.setPositionName("宁波市普法教育中心");
        dto2.setCity("宁波市");
        dto2.setArea("海曙区");
        dto2.setPositionLevel("市级");
        dto2.setPositionType("普法教育基地");
        dto2.setAddressDetail("中山路456号");
        dto2.setDescription("宁波市普法教育重要场所");
        dataList.add(dto2);

        LawPositionExcelDto dto3 = new LawPositionExcelDto();
        dto3.setPositionName("温州市法治宣传点");
        dto3.setCity("温州市");
        dto3.setArea("鹿城区");
        dto3.setPositionLevel("区级");
        dto3.setPositionType("法治宣传点");
        dto3.setAddressDetail("人民路789号");
        dto3.setDescription("温州市基层法治宣传阵地");
        dataList.add(dto3);

        return dataList;
    }

    /**
     * 创建测试Excel文件
     */
    private void createTestExcelFile(List<LawPositionExcelDto> data, String filePath) {
        try {
            ExportParams params = new ExportParams("普法阵地导入模板", "普法阵地数据");
            Workbook workbook = ExcelExportUtil.exportExcel(params, LawPositionExcelDto.class, data);
            
            FileUtil.mkParentDirs(filePath);
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
            workbook.close();
            
            log.info("测试Excel文件创建成功：{}", filePath);
        } catch (IOException e) {
            log.error("创建测试Excel文件失败：{}", e.getMessage(), e);
            fail("创建测试Excel文件失败");
        }
    }

    /**
     * 导入Excel文件
     */
    private List<LawPositionExcelDto> importExcelFile(String filePath) {
        try {
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            
            List<LawPositionExcelDto> list = ExcelImportUtil.importExcel(
                new File(filePath), LawPositionExcelDto.class, params);
            
            log.info("Excel文件导入成功，共{}条数据", list.size());
            return list;
        } catch (Exception e) {
            log.error("导入Excel文件失败：{}", e.getMessage(), e);
            fail("导入Excel文件失败");
            return null;
        }
    }

    /**
     * 转换Excel数据为LawPosition实体
     */
    private LawPosition convertToLawPosition(LawPositionExcelDto dto) {
        // 验证必填字段
        List<String> errors = validateRequiredFields(dto);
        if (!errors.isEmpty()) {
            log.warn("数据验证失败：{}", errors);
            return null;
        }

        LawPosition lawPosition = new LawPosition();
        lawPosition.setPositionName(dto.getPositionName().trim());
        lawPosition.setCity(dto.getCity().trim());
        lawPosition.setArea(dto.getArea().trim());
        lawPosition.setDescription(StrUtil.isNotBlank(dto.getDescription()) ? dto.getDescription().trim() : null);
        lawPosition.setAddressDetail(StrUtil.isNotBlank(dto.getAddressDetail()) ? dto.getAddressDetail().trim() : null);

        // 转换阵地类型
        String positionType = convertPositionType(dto.getPositionType().trim());
        if (positionType == null) {
            log.warn("未知的阵地类型：{}", dto.getPositionType());
            return null;
        }
        lawPosition.setPositionType(positionType);

        // 转换阵地级别
        String positionLevel = convertPositionLevel(dto.getPositionLevel().trim());
        if (positionLevel == null) {
            log.warn("未知的阵地级别：{}", dto.getPositionLevel());
            return null;
        }
        lawPosition.setPositionLevel(positionLevel);

        // 生成地址信息
        generateAddressInfo(lawPosition);

        return lawPosition;
    }

    /**
     * 验证必填字段
     */
    private List<String> validateRequiredFields(LawPositionExcelDto dto) {
        List<String> errors = new ArrayList<>();

        if (StrUtil.isBlank(dto.getPositionName())) {
            errors.add("阵地名称不能为空");
        }
        if (StrUtil.isBlank(dto.getCity())) {
            errors.add("所在城市不能为空");
        }
        if (StrUtil.isBlank(dto.getArea())) {
            errors.add("所在地区不能为空");
        }
        if (StrUtil.isBlank(dto.getPositionLevel())) {
            errors.add("阵地级别不能为空");
        }
        if (StrUtil.isBlank(dto.getPositionType())) {
            errors.add("阵地类型不能为空");
        }

        return errors;
    }

    /**
     * 转换阵地类型
     */
    private String convertPositionType(String type) {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("法治文化基地", "1");
        typeMap.put("法治文化公园", "1");
        typeMap.put("普法教育基地", "2");
        typeMap.put("普法教育中心", "2");
        typeMap.put("法治宣传点", "3");
        typeMap.put("民主法治村", "4");
        
        return typeMap.get(type);
    }

    /**
     * 转换阵地级别
     */
    private String convertPositionLevel(String level) {
        Map<String, String> levelMap = new HashMap<>();
        levelMap.put("省级", "1");
        levelMap.put("市级", "2");
        levelMap.put("区级", "3");
        levelMap.put("县级", "3");
        levelMap.put("乡镇级", "4");
        levelMap.put("街道级", "4");
        
        return levelMap.get(level);
    }

    /**
     * 生成地址信息
     */
    private void generateAddressInfo(LawPosition lawPosition) {
        // 简化版地址信息生成
        lawPosition.setAddressName("浙江省" + lawPosition.getCity() + lawPosition.getArea());
        lawPosition.setAddressCode("330000,330100,330106"); // 示例编码
    }

    /**
     * 检查重复数据
     */
    private boolean checkDuplicate(String positionName, String city, String area) {
        long count = lawPositionService.count(
            new QueryWrapper<LawPosition>()
                .eq("position_name", positionName)
                .eq("city", city)
                .eq("area", area)
        );
        return count > 0;
    }

    /**
     * 创建有效的DTO对象
     */
    private LawPositionExcelDto createValidDto() {
        LawPositionExcelDto dto = new LawPositionExcelDto();
        dto.setPositionName("测试阵地");
        dto.setCity("杭州市");
        dto.setArea("西湖区");
        dto.setPositionLevel("市级");
        dto.setPositionType("法治文化基地");
        return dto;
    }
}
