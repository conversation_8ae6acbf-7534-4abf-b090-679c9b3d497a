package com.concise.sys.modular.emp.param;

import cn.hutool.core.lang.Dict;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 员工参数
 *
 * <AUTHOR>
 * @date 2020/4/1 19:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysEmpParam extends BaseParam {

    /**
     * 主键
     */
    private String id;

    /**
     * 用户id
     */
    private String jobNum;

    /**
     * 所属机构id
     */
    @NotNull(message = "所属机构id不能为空，请检查sysEmpParam.orgId参数", groups = {add.class, edit.class})
    private String orgId;

    /**
     * 所属机构名称
     */
    private String orgName;
}
